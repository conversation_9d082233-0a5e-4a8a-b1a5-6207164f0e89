package repo

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

// PMO Collaborator Repository
var PMOCollaborator = repository.Make[models.PMOCollaborator]()

func PMOCollaboratorOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tab_key ASC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCollaboratorWithProject() repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		c.Preload("Project")
	}
}

func PMOCollaboratorWithUser() repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		c.Preload("User")
	}
}

func PMOCollaboratorByProjectID(projectID string) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCollaboratorByTabKey(tabKey string) repository.Option[models.PMOCollaborator] {
	return func(c repository.IRepository[models.PMOCollaborator]) {
		if tabKey != "" {
			c.Where("tab_key = ?", tabKey)
		}
	}
}

// PMO Comment Repository
var PMOComment = repository.Make[models.PMOComment]()

func PMOCommentOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCommentWithProject() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("Project")
	}
}

func PMOCommentWithUser() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("User")
	}
}

func PMOCommentWithVersions() repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		c.Preload("CommentVersions")
	}
}

func PMOCommentByProjectID(projectID string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOCommentByChannel(channel string) repository.Option[models.PMOComment] {
	return func(c repository.IRepository[models.PMOComment]) {
		if channel != "" {
			c.Where("channel = ?", channel)
		}
	}
}

// PMO Comment Version Repository
var PMOCommentVersion = repository.Make[models.PMOCommentVersion]()

func PMOCommentVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOCommentVersionByCommentID(commentID string) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if commentID != "" {
			c.Where("comment_id = ?", commentID)
		}
	}
}

func PMOCommentVersionByProjectID(projectID string) repository.Option[models.PMOCommentVersion] {
	return func(c repository.IRepository[models.PMOCommentVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

// PMO Budget Info Repository
var PMOBudgetInfo = repository.Make[models.PMOBudgetInfo]()

func PMOBudgetInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBudgetInfo] {
	return func(c repository.IRepository[models.PMOBudgetInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBudgetInfoByProjectID(projectID string) repository.Option[models.PMOBudgetInfo] {
	return func(c repository.IRepository[models.PMOBudgetInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

// PMO Budget Info Version Repository
var PMOBudgetInfoVersion = repository.Make[models.PMOBudgetInfoVersion]()

func PMOBudgetInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBudgetInfoVersionByBudgetInfoID(budgetInfoID string) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if budgetInfoID != "" {
			c.Where("budget_info_id = ?", budgetInfoID)
		}
	}
}

func PMOBudgetInfoVersionByProjectID(projectID string) repository.Option[models.PMOBudgetInfoVersion] {
	return func(c repository.IRepository[models.PMOBudgetInfoVersion]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

// PMO Bidding Info Repository
var PMOBiddingInfo = repository.Make[models.PMOBiddingInfo]()

func PMOBiddingInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("tender_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBiddingInfoByProjectID(projectID string) repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOBiddingInfoWithVersions() repository.Option[models.PMOBiddingInfo] {
	return func(c repository.IRepository[models.PMOBiddingInfo]) {
		c.Preload("BiddingInfoVersions")
	}
}

// PMO Bidding Info Version Repository
var PMOBiddingInfoVersion = repository.Make[models.PMOBiddingInfoVersion]()

func PMOBiddingInfoVersionOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOBiddingInfoVersion] {
	return func(c repository.IRepository[models.PMOBiddingInfoVersion]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOBiddingInfoVersionByBiddingInfoID(biddingInfoID string) repository.Option[models.PMOBiddingInfoVersion] {
	return func(c repository.IRepository[models.PMOBiddingInfoVersion]) {
		if biddingInfoID != "" {
			c.Where("bidding_info_id = ?", biddingInfoID)
		}
	}
}

// PMO Contract Info Repository
var PMOContractInfo = repository.Make[models.PMOContractInfo]()

func PMOContractInfoOrderBy(pageOptions *core.PageOptions) repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("signing_date DESC, created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func PMOContractInfoByProjectID(projectID string) repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		if projectID != "" {
			c.Where("project_id = ?", projectID)
		}
	}
}

func PMOContractInfoWithVersions() repository.Option[models.PMOContractInfo] {
	return func(c repository.IRepository[models.PMOContractInfo]) {
		c.Preload("ContractInfoVersions")
	}
}

// PMO Contract Info Version Repository
var PMOContractInfoVersion = repository.Make[models.PMOContractInfoVersion]()

func PMOContractInfoVersionByContractInfoID(contractInfoID string) repository.Option[models.PMOContractInfoVersion] {
	return func(c repository.IRepository[models.PMOContractInfoVersion]) {
		if contractInfoID != "" {
			c.Where("contract_info_id = ?", contractInfoID)
		}
	}
}
