package models

import (
	"github.com/lib/pq"
)

type PMOProjectStatus string

const (
	PMOProjectStatusDraft    PMOProjectStatus = "DRAFT"
	PMOProjectStatusTor      PMOProjectStatus = "TOR"
	PMOProjectStatusBidding  PMOProjectStatus = "BIDDING"
	PMOProjectStatusPMO      PMOProjectStatus = "PMO"
	PMOProjectStatusWarranty PMOProjectStatus = "WARRANTY"
	PMOProjectStatusClosed   PMOProjectStatus = "CLOSED"
	PMOProjectStatusCancel   PMOProjectStatus = "CANCEL"
)

var PMOProjectStatuses = []string{
	string(PMOProjectStatusDraft),
	string(PMOProjectStatusTor),
	string(PMOProjectStatusBidding),
	string(PMOProjectStatusPMO),
	string(PMOProjectStatusWarranty),
	string(PMOProjectStatusClosed),
	string(PMOProjectStatusCancel),
}

type PMOTabPermission string

const (
	PMOTabPermissionReadonly PMOTabPermission = "READONLY"
	PMOTabPermissionModify   PMOTabPermission = "MODIFY"
)

var PMOTabPermissions = []string{
	string(PMOTabPermissionReadonly),
	string(PMOTabPermissionModify),
}

type PMOCommentChannel string

const (
	PMOCommentChannelOverall      PMOCommentChannel = "OVERALL"
	PMOCommentChannelConfidential PMOCommentChannel = "CONFIDENTIAL"
	PMOCommentChannelSales        PMOCommentChannel = "SALES"
	PMOCommentChannelPresales     PMOCommentChannel = "PRESALES"
	PMOCommentChannelBidding      PMOCommentChannel = "BIDDING"
	PMOCommentChannelPMOProgress  PMOCommentChannel = "PMO_PROGRESS"
	PMOCommentChannelPMOHWSW      PMOCommentChannel = "PMO_HWSW"
	PMOCommentChannelPMOWarranty  PMOCommentChannel = "PMO_WARRANTY"
	PMOCommentChannelPMOLegal     PMOCommentChannel = "PMO_LEGAL"
	PMOCommentChannelBizco        PMOCommentChannel = "BIZCO"
)

var PMOCommentChannels = []string{
	string(PMOCommentChannelOverall),
	string(PMOCommentChannelConfidential),
	string(PMOCommentChannelSales),
	string(PMOCommentChannelPresales),
	string(PMOCommentChannelBidding),
	string(PMOCommentChannelPMOProgress),
	string(PMOCommentChannelPMOHWSW),
	string(PMOCommentChannelPMOWarranty),
	string(PMOCommentChannelPMOLegal),
	string(PMOCommentChannelBizco),
}

type PMODocType string

const (
	PMODocTypeInbound  PMODocType = "INBOUND"
	PMODocTypeOutbound PMODocType = "OUTBOUND"
)

var PMODocTypes = []string{
	string(PMODocTypeInbound),
	string(PMODocTypeOutbound),
}

type PMOProject struct {
	BaseModelWithAudit
	Name      string           `json:"name" gorm:"column:name"`
	Slug      string           `json:"slug" gorm:"column:slug"`
	Email     string           `json:"email" gorm:"column:email"`
	Tags      pq.StringArray   `json:"tags" gorm:"column:tags;type:text[]"`
	Status    PMOProjectStatus `json:"status" gorm:"column:status"`
	ProjectID string           `json:"project_id" gorm:"column:project_id;type:uuid"`

	// Relations
	Project              *Project                 `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Collaborators        []PMOCollaborator        `json:"collaborators,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Comments             []PMOComment             `json:"comments,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	CommentVersions      []PMOCommentVersion      `json:"comment_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Remarks              []PMORemark              `json:"remarks,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	RemarkVersions       []PMORemarkVersion       `json:"remark_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentGroups       []PMODocumentGroup       `json:"document_groups,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItems        []PMODocumentItem        `json:"document_items,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItemVersions []PMODocumentItemVersion `json:"document_item_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Contacts             []PMOContact             `json:"contacts,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Competitors          []PMOCompetitor          `json:"competitors,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Partners             []PMOPartner             `json:"partners,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BudgetInfo           []PMOBudgetInfo          `json:"budget_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BudgetInfoVersions   []PMOBudgetInfoVersion   `json:"budget_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BiddingInfo          []PMOBiddingInfo         `json:"bidding_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BiddingInfoVersions  []PMOBiddingInfoVersion  `json:"bidding_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	ContractInfo         []PMOContractInfo        `json:"contract_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	ContractInfoVersions []PMOContractInfoVersion `json:"contract_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BidbondInfo          []PMOBidbondInfo         `json:"bidbond_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BidbondInfoVersions  []PMOBidbondInfoVersion  `json:"bidbond_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	LGInfo               []PMOLGInfo              `json:"lg_info,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	LGInfoVersions       []PMOLGInfoVersion       `json:"lg_info_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	VendorItems          []PMOVendorItem          `json:"vendor_items,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	VendorItemVersions   []PMOVendorItemVersion   `json:"vendor_item_versions,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOProject) TableName() string {
	return "pmo_projects"
}
