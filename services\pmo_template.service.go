package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type IPMOTemplateService interface {
	// PMO Template Documents
	DocumentsCreate(input *PMOTemplateDocumentCreatePayload) (*models.PMOTemplateDocument, core.IError)
	DocumentsUpdate(id string, input *PMOTemplateDocumentUpdatePayload) (*models.PMOTemplateDocument, core.IError)
	DocumentsFind(id string) (*models.PMOTemplateDocument, core.IError)
	DocumentsPagination(pageOptions *core.PageOptions, options *PMOTemplateDocumentPaginationOptions) (*repository.Pagination[models.PMOTemplateDocument], core.IError)
	DocumentsDelete(id string) core.IError

	// PMO Template Checklist Items
	ChecklistItemsCreate(input *PMOTemplateChecklistItemCreatePayload) (*models.PMOTemplateChecklistItem, core.IError)
	ChecklistItemsUpdate(id string, input *PMOTemplateChecklistItemUpdatePayload) (*models.PMOTemplateChecklistItem, core.IError)
	ChecklistItemsFind(id string) (*models.PMOTemplateChecklistItem, core.IError)
	ChecklistItemsPagination(pageOptions *core.PageOptions, options *PMOTemplateChecklistItemPaginationOptions) (*repository.Pagination[models.PMOTemplateChecklistItem], core.IError)
	ChecklistItemsDelete(id string) core.IError
}

type pmoTemplateService struct {
	ctx core.IContext
}

// PMO Template Documents implementation
func (s pmoTemplateService) DocumentsCreate(input *PMOTemplateDocumentCreatePayload) (*models.PMOTemplateDocument, core.IError) {
	document := &models.PMOTemplateDocument{
		BaseModel:     models.NewBaseModel(),
		TabKey:        models.PMOTabKey(input.TabKey),
		Name:          input.Name,
		SharepointURL: input.SharepointURL,
		CreatedByID:   utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID:   utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.PMOTemplateDocument(s.ctx).Create(document)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.DocumentsFind(document.ID)
}

func (s pmoTemplateService) DocumentsUpdate(id string, input *PMOTemplateDocumentUpdatePayload) (*models.PMOTemplateDocument, core.IError) {
	document, ierr := s.DocumentsFind(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.TabKey != "" {
		document.TabKey = models.PMOTabKey(input.TabKey)
	}
	if input.Name != "" {
		document.Name = input.Name
	}
	if input.SharepointURL != "" {
		document.SharepointURL = input.SharepointURL
	}

	document.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repo.PMOTemplateDocument(s.ctx).Where("id = ?", id).Updates(document)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.DocumentsFind(document.ID)
}

func (s pmoTemplateService) DocumentsFind(id string) (*models.PMOTemplateDocument, core.IError) {
	return repo.PMOTemplateDocument(s.ctx).FindOne("id = ?", id)
}

func (s pmoTemplateService) DocumentsPagination(pageOptions *core.PageOptions, options *PMOTemplateDocumentPaginationOptions) (*repository.Pagination[models.PMOTemplateDocument], core.IError) {
	return repo.PMOTemplateDocument(s.ctx,
		repo.PMOTemplateDocumentOrderBy(pageOptions),
		repo.PMOTemplateDocumentWithSearch(pageOptions.Q),
		repo.PMOTemplateDocumentWithTabKeyFilter(options.TabKey)).Pagination(pageOptions)
}

func (s pmoTemplateService) DocumentsDelete(id string) core.IError {
	_, ierr := s.DocumentsFind(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.PMOTemplateDocument(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

// PMO Template Checklist Items implementation
func (s pmoTemplateService) ChecklistItemsCreate(input *PMOTemplateChecklistItemCreatePayload) (*models.PMOTemplateChecklistItem, core.IError) {
	item := &models.PMOTemplateChecklistItem{
		BaseModel:   models.NewBaseModel(),
		TabKey:      models.PMOTabKey(input.TabKey),
		Detail:      input.Detail,
		CreatedByID: utils.ToPointer(s.ctx.GetUser().ID),
		UpdatedByID: utils.ToPointer(s.ctx.GetUser().ID),
	}

	ierr := repo.PMOTemplateChecklistItem(s.ctx).Create(item)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.ChecklistItemsFind(item.ID)
}

func (s pmoTemplateService) ChecklistItemsUpdate(id string, input *PMOTemplateChecklistItemUpdatePayload) (*models.PMOTemplateChecklistItem, core.IError) {
	item, ierr := s.ChecklistItemsFind(id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update fields if provided
	if input.TabKey != "" {
		item.TabKey = models.PMOTabKey(input.TabKey)
	}
	if input.Detail != "" {
		item.Detail = input.Detail
	}

	item.UpdatedByID = utils.ToPointer(s.ctx.GetUser().ID)

	ierr = repo.PMOTemplateChecklistItem(s.ctx).Where("id = ?", id).Updates(item)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.ChecklistItemsFind(item.ID)
}

func (s pmoTemplateService) ChecklistItemsFind(id string) (*models.PMOTemplateChecklistItem, core.IError) {
	return repo.PMOTemplateChecklistItem(s.ctx).FindOne("id = ?", id)
}

func (s pmoTemplateService) ChecklistItemsPagination(pageOptions *core.PageOptions, options *PMOTemplateChecklistItemPaginationOptions) (*repository.Pagination[models.PMOTemplateChecklistItem], core.IError) {
	return repo.PMOTemplateChecklistItem(s.ctx,
		repo.PMOTemplateChecklistItemOrderBy(pageOptions),
		repo.PMOTemplateChecklistItemWithSearch(pageOptions.Q),
		repo.PMOTemplateChecklistItemWithTabKeyFilter(options.TabKey)).Pagination(pageOptions)
}

func (s pmoTemplateService) ChecklistItemsDelete(id string) core.IError {
	_, ierr := s.ChecklistItemsFind(id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	return repo.PMOTemplateChecklistItem(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func NewPMOTemplateService(ctx core.IContext) IPMOTemplateService {
	return &pmoTemplateService{ctx: ctx}
}
