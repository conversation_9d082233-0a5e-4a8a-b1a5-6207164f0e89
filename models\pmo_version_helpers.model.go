package models

import (
	"gitlab.finema.co/finema/idin-core/utils"
)

// VersionableModel interface for models that support versioning
type VersionableModel interface {
	GetID() string
	GetProjectID() string
}

// VersionModel interface for version models
type VersionModel interface {
	GetOriginalID() string
	GetProjectID() string
	SetOriginalID(string)
	SetProjectID(string)
}

// CreateVersion creates a version record from a main model
func CreateVersion[T VersionableModel, V VersionModel](original T, version V, userID *string) V {
	version.SetOriginalID(original.GetID())
	version.SetProjectID(original.GetProjectID())
	
	// Set audit fields if the version model supports it
	if auditable, ok := any(version).(interface{ SetCreatedByID(*string) }); ok {
		auditable.SetCreatedByID(userID)
	}
	
	return version
}

// PMOBudgetInfo implements VersionableModel
func (p PMOBudgetInfo) GetID() string {
	return p.ID
}

func (p PMOBudgetInfo) GetProjectID() string {
	return p.ProjectID
}

// PMOBudgetInfoVersion implements VersionModel
func (p PMOBudgetInfoVersion) GetOriginalID() string {
	return p.BudgetInfoID
}

func (p PMOBudgetInfoVersion) GetProjectID() string {
	return p.ProjectID
}

func (p *PMOBudgetInfoVersion) SetOriginalID(id string) {
	p.BudgetInfoID = id
}

func (p *PMOBudgetInfoVersion) SetProjectID(id string) {
	p.ProjectID = id
}

// PMOBiddingInfo implements VersionableModel
func (p PMOBiddingInfo) GetID() string {
	return p.ID
}

func (p PMOBiddingInfo) GetProjectID() string {
	return p.ProjectID
}

// PMOBiddingInfoVersion implements VersionModel
func (p PMOBiddingInfoVersion) GetOriginalID() string {
	return p.BiddingInfoID
}

func (p PMOBiddingInfoVersion) GetProjectID() string {
	return p.ProjectID
}

func (p *PMOBiddingInfoVersion) SetOriginalID(id string) {
	p.BiddingInfoID = id
}

func (p *PMOBiddingInfoVersion) SetProjectID(id string) {
	p.ProjectID = id
}

// PMOContractInfo implements VersionableModel
func (p PMOContractInfo) GetID() string {
	return p.ID
}

func (p PMOContractInfo) GetProjectID() string {
	return p.ProjectID
}

// PMOContractInfoVersion implements VersionModel
func (p PMOContractInfoVersion) GetOriginalID() string {
	return p.ContractInfoID
}

func (p PMOContractInfoVersion) GetProjectID() string {
	return p.ProjectID
}

func (p *PMOContractInfoVersion) SetOriginalID(id string) {
	p.ContractInfoID = id
}

func (p *PMOContractInfoVersion) SetProjectID(id string) {
	p.ProjectID = id
}

// PMOBidbondInfo implements VersionableModel
func (p PMOBidbondInfo) GetID() string {
	return p.ID
}

func (p PMOBidbondInfo) GetProjectID() string {
	return p.ProjectID
}

// PMOBidbondInfoVersion implements VersionModel
func (p PMOBidbondInfoVersion) GetOriginalID() string {
	return p.BidbondInfoID
}

func (p PMOBidbondInfoVersion) GetProjectID() string {
	return p.ProjectID
}

func (p *PMOBidbondInfoVersion) SetOriginalID(id string) {
	p.BidbondInfoID = id
}

func (p *PMOBidbondInfoVersion) SetProjectID(id string) {
	p.ProjectID = id
}

// PMOLGInfo implements VersionableModel
func (p PMOLGInfo) GetID() string {
	return p.ID
}

func (p PMOLGInfo) GetProjectID() string {
	return p.ProjectID
}

// PMOLGInfoVersion implements VersionModel
func (p PMOLGInfoVersion) GetOriginalID() string {
	return p.LGInfoID
}

func (p PMOLGInfoVersion) GetProjectID() string {
	return p.ProjectID
}

func (p *PMOLGInfoVersion) SetOriginalID(id string) {
	p.LGInfoID = id
}

func (p *PMOLGInfoVersion) SetProjectID(id string) {
	p.ProjectID = id
}

// PMOVendorItem implements VersionableModel
func (p PMOVendorItem) GetID() string {
	return p.ID
}

func (p PMOVendorItem) GetProjectID() string {
	return p.ProjectID
}

// PMOVendorItemVersion implements VersionModel
func (p PMOVendorItemVersion) GetOriginalID() string {
	return p.VendorItemID
}

func (p PMOVendorItemVersion) GetProjectID() string {
	return p.ProjectID
}

func (p *PMOVendorItemVersion) SetOriginalID(id string) {
	p.VendorItemID = id
}

func (p *PMOVendorItemVersion) SetProjectID(id string) {
	p.ProjectID = id
}

// Helper function to create a new version with proper ID generation
func NewVersionFromOriginal[T VersionableModel, V VersionModel](original T, versionConstructor func() V, userID *string) V {
	version := versionConstructor()
	
	// Set base fields
	if baseModel, ok := any(&version).(interface{ SetID(string) }); ok {
		baseModel.SetID(utils.GetUUID())
	}
	
	// Set timestamps
	if timestamped, ok := any(&version).(interface{ SetCreatedAt(*time.Time) }); ok {
		now := utils.GetCurrentDateTime()
		timestamped.SetCreatedAt(now)
		if updatable, ok := any(&version).(interface{ SetUpdatedAt(*time.Time) }); ok {
			updatable.SetUpdatedAt(now)
		}
	}
	
	return CreateVersion(original, version, userID)
}
