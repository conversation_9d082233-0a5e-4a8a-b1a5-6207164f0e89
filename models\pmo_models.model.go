package models

import (
	"time"
)

// PMOCollaborator represents project collaborators
type PMOCollaborator struct {
	BaseModelWithAudit
	ProjectID       string           `json:"project_id" gorm:"column:project_id;type:uuid"`
	UserID          string           `json:"user_id" gorm:"column:user_id;type:uuid"`
	TabKey          PMOTabKey        `json:"tab_key" gorm:"column:tab_key"`
	TabPermission   PMOTabPermission `json:"tab_permission" gorm:"column:tab_permission"`
	TabMainResponse bool             `json:"tab_main_response" gorm:"column:tab_main_response"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	User    *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

func (PMOCollaborator) TableName() string {
	return "pmo_collaborators"
}

// PMOComment represents project comments
type PMOComment struct {
	BaseModelWithAudit
	ProjectID       string            `json:"project_id" gorm:"column:project_id;type:uuid"`
	UserID          string            `json:"user_id" gorm:"column:user_id;type:uuid"`
	Channel         PMOCommentChannel `json:"channel" gorm:"column:channel"`
	Detail          string            `json:"detail" gorm:"column:detail"`
	IsClientFlag    bool              `json:"is_client_flag" gorm:"column:is_client_flag"`
	ParentCommentID *string           `json:"parent_comment_id" gorm:"column:parent_comment_id;type:uuid"`

	// Relations
	Project         *PMOProject         `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	User            *User               `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
	CommentVersions []PMOCommentVersion `json:"comment_versions,omitempty" gorm:"foreignKey:CommentID;references:ID"`
}

func (PMOComment) TableName() string {
	return "pmo_comments"
}

// PMOCommentVersion represents versioned project comments
type PMOCommentVersion struct {
	BaseVersionModel
	CommentID       string            `json:"comment_id" gorm:"column:comment_id;type:uuid"`
	UserID          string            `json:"user_id" gorm:"column:user_id;type:uuid"`
	Channel         PMOCommentChannel `json:"channel" gorm:"column:channel"`
	Detail          string            `json:"detail" gorm:"column:detail"`
	IsClientFlag    bool              `json:"is_client_flag" gorm:"column:is_client_flag"`
	ParentCommentID *string           `json:"parent_comment_id" gorm:"column:parent_comment_id;type:uuid"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Comment *PMOComment `json:"comment,omitempty" gorm:"foreignKey:CommentID;references:ID"`
	User    *User       `json:"user,omitempty" gorm:"foreignKey:UserID;references:ID"`
}

func (PMOCommentVersion) TableName() string {
	return "pmo_comment_versions"
}

// PMOChecklistItem represents checklist items
type PMOChecklistItem struct {
	BaseModelWithAudit
	TabKey    PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail    string    `json:"detail" gorm:"column:detail"`
	IsChecked bool      `json:"is_checked" gorm:"column:is_checked"`
}

func (PMOChecklistItem) TableName() string {
	return "pmo_checklist_items"
}

// PMORemark represents project remarks
type PMORemark struct {
	BaseModelWithAudit
	ProjectID string    `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey    PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail    string    `json:"detail" gorm:"column:detail"`

	// Relations
	Project        *PMOProject        `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	RemarkVersions []PMORemarkVersion `json:"remark_versions,omitempty" gorm:"foreignKey:RemarkID;references:ID"`
}

func (PMORemark) TableName() string {
	return "pmo_remarks"
}

// PMORemarkVersion represents versioned project remarks
type PMORemarkVersion struct {
	BaseVersionModel
	RemarkID string    `json:"remark_id" gorm:"column:remark_id;type:uuid"`
	TabKey   PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	Detail   string    `json:"detail" gorm:"column:detail"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Remark  *PMORemark  `json:"remark,omitempty" gorm:"foreignKey:RemarkID;references:ID"`
}

func (PMORemarkVersion) TableName() string {
	return "pmo_remark_versions"
}

// PMODocumentGroup represents document groups
type PMODocumentGroup struct {
	BaseModelWithAudit
	ProjectID string    `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey    PMOTabKey `json:"tab_key" gorm:"column:tab_key"`
	GroupName string    `json:"group_name" gorm:"column:group_name"`

	// Relations
	Project       *PMOProject       `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItems []PMODocumentItem `json:"document_items,omitempty" gorm:"foreignKey:GroupID;references:ID"`
}

func (PMODocumentGroup) TableName() string {
	return "pmo_document_groups"
}

// PMODocumentItem represents document items
type PMODocumentItem struct {
	BaseModelWithAudit
	ProjectID     string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	TabKey        PMOTabKey  `json:"tab_key" gorm:"column:tab_key"`
	GroupID       string     `json:"group_id" gorm:"column:group_id;type:uuid"`
	Name          string     `json:"name" gorm:"column:name"`
	SharepointURL string     `json:"sharepoint_url" gorm:"column:sharepoint_url"`
	Date          *time.Time `json:"date" gorm:"column:date;type:date"`
	Type          PMODocType `json:"type" gorm:"column:type"`
	FileID        *string    `json:"file_id" gorm:"column:file_id;type:uuid"`

	// Relations
	Project              *PMOProject              `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	Group                *PMODocumentGroup        `json:"group,omitempty" gorm:"foreignKey:GroupID;references:ID"`
	File                 *File                    `json:"file,omitempty" gorm:"foreignKey:FileID;references:ID"`
	DocumentItemVersions []PMODocumentItemVersion `json:"document_item_versions,omitempty" gorm:"foreignKey:DocumentItemID;references:ID"`
}

func (PMODocumentItem) TableName() string {
	return "pmo_document_items"
}

// PMODocumentItemVersion represents versioned document items
type PMODocumentItemVersion struct {
	BaseVersionModel
	DocumentItemID string     `json:"document_item_id" gorm:"column:document_item_id;type:uuid"`
	TabKey         PMOTabKey  `json:"tab_key" gorm:"column:tab_key"`
	GroupID        string     `json:"group_id" gorm:"column:group_id;type:uuid"`
	Name           string     `json:"name" gorm:"column:name"`
	SharepointURL  string     `json:"sharepoint_url" gorm:"column:sharepoint_url"`
	Date           *time.Time `json:"date" gorm:"column:date;type:date"`
	Type           PMODocType `json:"type" gorm:"column:type"`
	FileID         *string    `json:"file_id" gorm:"column:file_id;type:uuid"`

	// Relations
	Project      *PMOProject      `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	DocumentItem *PMODocumentItem `json:"document_item,omitempty" gorm:"foreignKey:DocumentItemID;references:ID"`
	File         *File            `json:"file,omitempty" gorm:"foreignKey:FileID;references:ID"`
}

func (PMODocumentItemVersion) TableName() string {
	return "pmo_document_item_versions"
}

// PMOContact represents project contacts
type PMOContact struct {
	BaseModelWithAudit
	ProjectID string `json:"project_id" gorm:"column:project_id;type:uuid"`
	Fullname  string `json:"fullname" gorm:"column:fullname"`
	Phone     string `json:"phone" gorm:"column:phone"`
	Email     string `json:"email" gorm:"column:email"`
	Detail    string `json:"detail" gorm:"column:detail"`
	Company   string `json:"company" gorm:"column:company"`
	Position  string `json:"position" gorm:"column:position"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOContact) TableName() string {
	return "pmo_contacts"
}

// PMOCompetitor represents project competitors
type PMOCompetitor struct {
	BaseModelWithAudit
	ProjectID string `json:"project_id" gorm:"column:project_id;type:uuid"`
	Company   string `json:"company" gorm:"column:company"`
	Detail    string `json:"detail" gorm:"column:detail"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOCompetitor) TableName() string {
	return "pmo_competitors"
}

// PMOPartner represents project partners
type PMOPartner struct {
	BaseModelWithAudit
	ProjectID string `json:"project_id" gorm:"column:project_id;type:uuid"`
	Company   string `json:"company" gorm:"column:company"`
	Detail    string `json:"detail" gorm:"column:detail"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOPartner) TableName() string {
	return "pmo_partners"
}

// PMOBudgetInfo represents project budget information
type PMOBudgetInfo struct {
	BaseModelWithAudit
	ProjectID    string  `json:"project_id" gorm:"column:project_id;type:uuid"`
	FundType     string  `json:"fund_type" gorm:"column:fund_type"`
	ProjectValue float64 `json:"project_value" gorm:"column:project_value"`
	BidbondValue float64 `json:"bidbond_value" gorm:"column:bidbond_value"`
	Partner      string  `json:"partner" gorm:"column:partner"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBudgetInfo) TableName() string {
	return "pmo_budget_info"
}

// PMOBudgetInfoVersion represents versioned budget information
type PMOBudgetInfoVersion struct {
	BaseVersionModel
	BudgetInfoID string  `json:"budget_info_id" gorm:"column:budget_info_id;type:uuid"`
	FundType     string  `json:"fund_type" gorm:"column:fund_type"`
	ProjectValue float64 `json:"project_value" gorm:"column:project_value"`
	BidbondValue float64 `json:"bidbond_value" gorm:"column:bidbond_value"`
	Partner      string  `json:"partner" gorm:"column:partner"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
}

func (PMOBudgetInfoVersion) TableName() string {
	return "pmo_budget_info_versions"
}

// PMOBiddingInfo represents project bidding information
type PMOBiddingInfo struct {
	BaseModelWithAudit
	ProjectID    string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	BiddingType  string     `json:"bidding_type" gorm:"column:bidding_type"`
	BiddingValue float64    `json:"bidding_value" gorm:"column:bidding_value"`
	TenderDate   *time.Time `json:"tender_date" gorm:"column:tender_date;type:date"`
	TenderEntity string     `json:"tender_entity" gorm:"column:tender_entity"`
	AnnounceDate *time.Time `json:"announce_date" gorm:"column:announce_date;type:date"`

	// Relations
	Project             *PMOProject             `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BiddingInfoVersions []PMOBiddingInfoVersion `json:"bidding_info_versions,omitempty" gorm:"foreignKey:BiddingInfoID;references:ID"`
}

func (PMOBiddingInfo) TableName() string {
	return "pmo_bidding_info"
}

// PMOBiddingInfoVersion represents versioned bidding information
type PMOBiddingInfoVersion struct {
	BaseVersionModel
	BiddingInfoID string     `json:"bidding_info_id" gorm:"column:bidding_info_id;type:uuid"`
	BiddingType   string     `json:"bidding_type" gorm:"column:bidding_type"`
	BiddingValue  float64    `json:"bidding_value" gorm:"column:bidding_value"`
	TenderDate    *time.Time `json:"tender_date" gorm:"column:tender_date;type:date"`
	TenderEntity  string     `json:"tender_entity" gorm:"column:tender_entity"`
	AnnounceDate  *time.Time `json:"announce_date" gorm:"column:announce_date;type:date"`

	// Relations
	Project     *PMOProject     `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BiddingInfo *PMOBiddingInfo `json:"bidding_info,omitempty" gorm:"foreignKey:BiddingInfoID;references:ID"`
}

func (PMOBiddingInfoVersion) TableName() string {
	return "pmo_bidding_info_versions"
}

// PMOContractInfo represents project contract information
type PMOContractInfo struct {
	BaseModelWithAudit
	ProjectID            string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	ContractNo           string     `json:"contract_no" gorm:"column:contract_no"`
	Value                float64    `json:"value" gorm:"column:value"`
	SigningDate          *time.Time `json:"signing_date" gorm:"column:signing_date;type:date"`
	StartDate            *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate              *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationDay          int        `json:"duration_day" gorm:"column:duration_day"`
	WarrantyDurationDay  int        `json:"warranty_duration_day" gorm:"column:warranty_duration_day"`
	WarrantyDurationYear int        `json:"warranty_duration_year" gorm:"column:warranty_duration_year"`
	Prime                string     `json:"prime" gorm:"column:prime"`
	PenaltyFee           float64    `json:"penalty_fee" gorm:"column:penalty_fee"`
	IsLegalizeStamp      bool       `json:"is_legalize_stamp" gorm:"column:is_legalize_stamp"`

	// Relations
	Project              *PMOProject              `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	ContractInfoVersions []PMOContractInfoVersion `json:"contract_info_versions,omitempty" gorm:"foreignKey:ContractInfoID;references:ID"`
}

func (PMOContractInfo) TableName() string {
	return "pmo_contract_info"
}

// PMOContractInfoVersion represents versioned contract information
type PMOContractInfoVersion struct {
	BaseVersionModel
	ContractInfoID       string     `json:"contract_info_id" gorm:"column:contract_info_id;type:uuid"`
	ContractNo           string     `json:"contract_no" gorm:"column:contract_no"`
	Value                float64    `json:"value" gorm:"column:value"`
	SigningDate          *time.Time `json:"signing_date" gorm:"column:signing_date;type:date"`
	StartDate            *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate              *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationDay          int        `json:"duration_day" gorm:"column:duration_day"`
	WarrantyDurationDay  int        `json:"warranty_duration_day" gorm:"column:warranty_duration_day"`
	WarrantyDurationYear int        `json:"warranty_duration_year" gorm:"column:warranty_duration_year"`
	Prime                string     `json:"prime" gorm:"column:prime"`
	PenaltyFee           float64    `json:"penalty_fee" gorm:"column:penalty_fee"`
	IsLegalizeStamp      bool       `json:"is_legalize_stamp" gorm:"column:is_legalize_stamp"`

	// Relations
	Project      *PMOProject      `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	ContractInfo *PMOContractInfo `json:"contract_info,omitempty" gorm:"foreignKey:ContractInfoID;references:ID"`
}

func (PMOContractInfoVersion) TableName() string {
	return "pmo_contract_info_versions"
}

// PMOBidbondInfo represents project bidbond information
type PMOBidbondInfo struct {
	BaseModelWithAudit
	ProjectID      string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	GuaranteeAsset string     `json:"guarantee_asset" gorm:"column:guarantee_asset"`
	BidbondPayer   string     `json:"bidbond_payer" gorm:"column:bidbond_payer"`
	BidbondValue   float64    `json:"bidbond_value" gorm:"column:bidbond_value"`
	StartDate      *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate        *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationMonth  int        `json:"duration_month" gorm:"column:duration_month"`
	DurationYear   int        `json:"duration_year" gorm:"column:duration_year"`
	Fee            float64    `json:"fee" gorm:"column:fee"`

	// Relations
	Project             *PMOProject             `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BidbondInfoVersions []PMOBidbondInfoVersion `json:"bidbond_info_versions,omitempty" gorm:"foreignKey:BidbondInfoID;references:ID"`
}

func (PMOBidbondInfo) TableName() string {
	return "pmo_bidbond_info"
}

// PMOBidbondInfoVersion represents versioned bidbond information
type PMOBidbondInfoVersion struct {
	BaseVersionModel
	BidbondInfoID  string     `json:"bidbond_info_id" gorm:"column:bidbond_info_id;type:uuid"`
	GuaranteeAsset string     `json:"guarantee_asset" gorm:"column:guarantee_asset"`
	BidbondPayer   string     `json:"bidbond_payer" gorm:"column:bidbond_payer"`
	BidbondValue   float64    `json:"bidbond_value" gorm:"column:bidbond_value"`
	StartDate      *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate        *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	DurationMonth  int        `json:"duration_month" gorm:"column:duration_month"`
	DurationYear   int        `json:"duration_year" gorm:"column:duration_year"`
	Fee            float64    `json:"fee" gorm:"column:fee"`

	// Relations
	Project     *PMOProject     `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	BidbondInfo *PMOBidbondInfo `json:"bidbond_info,omitempty" gorm:"foreignKey:BidbondInfoID;references:ID"`
}

func (PMOBidbondInfoVersion) TableName() string {
	return "pmo_bidbond_info_versions"
}

// PMOLGInfo represents project letter of guarantee information
type PMOLGInfo struct {
	BaseModelWithAudit
	ProjectID string     `json:"project_id" gorm:"column:project_id;type:uuid"`
	Value     float64    `json:"value" gorm:"column:value"`
	StartDate *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate   *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	Fee       float64    `json:"fee" gorm:"column:fee"`
	Interest  float64    `json:"interest" gorm:"column:interest"`

	// Relations
	Project        *PMOProject        `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	LGInfoVersions []PMOLGInfoVersion `json:"lg_info_versions,omitempty" gorm:"foreignKey:LGInfoID;references:ID"`
}

func (PMOLGInfo) TableName() string {
	return "pmo_lg_info"
}

// PMOLGInfoVersion represents versioned LG information
type PMOLGInfoVersion struct {
	BaseVersionModel
	LGInfoID  string     `json:"lg_info_id" gorm:"column:lg_info_id;type:uuid"`
	Value     float64    `json:"value" gorm:"column:value"`
	StartDate *time.Time `json:"start_date" gorm:"column:start_date;type:date"`
	EndDate   *time.Time `json:"end_date" gorm:"column:end_date;type:date"`
	Fee       float64    `json:"fee" gorm:"column:fee"`
	Interest  float64    `json:"interest" gorm:"column:interest"`

	// Relations
	Project *PMOProject `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	LGInfo  *PMOLGInfo  `json:"lg_info,omitempty" gorm:"foreignKey:LGInfoID;references:ID"`
}

func (PMOLGInfoVersion) TableName() string {
	return "pmo_lg_info_versions"
}

// PMOVendorItem represents project vendor items
type PMOVendorItem struct {
	BaseModelWithAudit
	ProjectID          string `json:"project_id" gorm:"column:project_id;type:uuid"`
	VendorName         string `json:"vendor_name" gorm:"column:vendor_name"`
	ItemName           string `json:"item_name" gorm:"column:item_name"`
	ItemDetail         string `json:"item_detail" gorm:"column:item_detail"`
	DeliverDurationDay int    `json:"deliver_duration_day" gorm:"column:deliver_duration_day"`
	IsTor              bool   `json:"is_tor" gorm:"column:is_tor"`
	IsImplementation   bool   `json:"is_implementation" gorm:"column:is_implementation"`
	IsTraining         bool   `json:"is_training" gorm:"column:is_training"`
	IsUserManual       bool   `json:"is_user_manual" gorm:"column:is_user_manual"`

	// Relations
	Project            *PMOProject            `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	VendorItemVersions []PMOVendorItemVersion `json:"vendor_item_versions,omitempty" gorm:"foreignKey:VendorItemID;references:ID"`
}

func (PMOVendorItem) TableName() string {
	return "pmo_vendor_items"
}

// PMOVendorItemVersion represents versioned vendor items
type PMOVendorItemVersion struct {
	BaseVersionModel
	VendorItemID       string `json:"vendor_item_id" gorm:"column:vendor_item_id;type:uuid"`
	VendorName         string `json:"vendor_name" gorm:"column:vendor_name"`
	ItemName           string `json:"item_name" gorm:"column:item_name"`
	ItemDetail         string `json:"item_detail" gorm:"column:item_detail"`
	DeliverDurationDay int    `json:"deliver_duration_day" gorm:"column:deliver_duration_day"`
	IsTor              bool   `json:"is_tor" gorm:"column:is_tor"`
	IsImplementation   bool   `json:"is_implementation" gorm:"column:is_implementation"`
	IsTraining         bool   `json:"is_training" gorm:"column:is_training"`
	IsUserManual       bool   `json:"is_user_manual" gorm:"column:is_user_manual"`

	// Relations
	Project    *PMOProject    `json:"project,omitempty" gorm:"foreignKey:ProjectID;references:ID"`
	VendorItem *PMOVendorItem `json:"vendor_item,omitempty" gorm:"foreignKey:VendorItemID;references:ID"`
}

func (PMOVendorItemVersion) TableName() string {
	return "pmo_vendor_items_versions"
}
