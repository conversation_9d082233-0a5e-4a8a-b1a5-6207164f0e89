package services

import (
	"gitlab.finema.co/finema/finework/finework-api/models"
	"gitlab.finema.co/finema/finework/finework-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type PMOBudgetInfoService interface {
	Create(data *models.PMOBudgetInfo) (*models.PMOBudgetInfo, core.IError)
	Update(id string, data *models.PMOBudgetInfo) (*models.PMOBudgetInfo, core.IError)
	UpdateWithVersion(id string, data *models.PMOBudgetInfo) (*models.PMOBudgetInfo, core.IError)
	Find(id string) (*models.PMOBudgetInfo, core.IError)
	FindByProjectID(projectID string) ([]models.PMOBudgetInfo, core.IError)
	Pagination(pageOptions *core.PageOptions, projectID string) (*repository.Pagination[models.PMOBudgetInfo], core.IError)
	Delete(id string) core.IError
	
	// Version-related methods
	GetVersionHistory(budgetInfoID string) ([]models.PMOBudgetInfoVersion, core.IError)
	GetVersionHistoryPagination(pageOptions *core.PageOptions, budgetInfoID string) (*repository.Pagination[models.PMOBudgetInfoVersion], core.IError)
	RestoreFromVersion(versionID string) (*models.PMOBudgetInfo, core.IError)
}

type pmoBudgetInfoService struct {
	ctx core.IContext
}

func NewPMOBudgetInfoService(ctx core.IContext) PMOBudgetInfoService {
	return &pmoBudgetInfoService{ctx: ctx}
}

func (s *pmoBudgetInfoService) Create(data *models.PMOBudgetInfo) (*models.PMOBudgetInfo, core.IError) {
	// Set base model fields
	data.BaseModelWithAudit = models.NewBaseModelWithAudit()
	data.CreatedByID = &s.ctx.GetUser().ID
	data.UpdatedByID = &s.ctx.GetUser().ID

	// Validate project exists
	if _, err := repo.PMOProject(s.ctx).FindOne("id = ?", data.ProjectID); err != nil {
		return nil, s.ctx.NewError(err, "Project not found")
	}

	// Create the budget info
	if err := repo.PMOBudgetInfo(s.ctx).Create(data); err != nil {
		return nil, s.ctx.NewError(err, err)
	}

	return data, nil
}

func (s *pmoBudgetInfoService) Update(id string, data *models.PMOBudgetInfo) (*models.PMOBudgetInfo, core.IError) {
	// Find existing record
	existing, err := s.Find(id)
	if err != nil {
		return nil, err
	}

	// Update fields
	data.ID = existing.ID
	data.CreatedAt = existing.CreatedAt
	data.CreatedByID = existing.CreatedByID
	data.UpdatedByID = &s.ctx.GetUser().ID
	data.UpdatedAt = utils.GetCurrentDateTime()

	// Save changes
	if ierr := repo.PMOBudgetInfo(s.ctx).Where("id = ?", id).Updates(data); ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return data, nil
}

func (s *pmoBudgetInfoService) UpdateWithVersion(id string, data *models.PMOBudgetInfo) (*models.PMOBudgetInfo, core.IError) {
	// Find existing record
	existing, err := s.Find(id)
	if err != nil {
		return nil, err
	}

	// Create version record from existing data before updating
	version := &models.PMOBudgetInfoVersion{
		BaseVersionModel: models.NewBaseVersionModel(existing.ID, existing.ProjectID),
		BudgetInfoID:     existing.ID,
		FundType:         existing.FundType,
		ProjectValue:     existing.ProjectValue,
		BidbondValue:     existing.BidbondValue,
		Partner:          existing.Partner,
	}
	version.CreatedByID = &s.ctx.GetUser().ID

	// Save version record
	if ierr := repo.PMOBudgetInfoVersion(s.ctx).Create(version); ierr != nil {
		return nil, s.ctx.NewError(ierr, "Failed to create version record")
	}

	// Now update the main record
	return s.Update(id, data)
}

func (s *pmoBudgetInfoService) Find(id string) (*models.PMOBudgetInfo, core.IError) {
	return repo.PMOBudgetInfo(s.ctx).FindOne("id = ?", id)
}

func (s *pmoBudgetInfoService) FindByProjectID(projectID string) ([]models.PMOBudgetInfo, core.IError) {
	return repo.PMOBudgetInfo(s.ctx, repo.PMOBudgetInfoByProjectID(projectID)).Find()
}

func (s *pmoBudgetInfoService) Pagination(pageOptions *core.PageOptions, projectID string) (*repository.Pagination[models.PMOBudgetInfo], core.IError) {
	return repo.PMOBudgetInfo(s.ctx,
		repo.PMOBudgetInfoOrderBy(pageOptions),
		repo.PMOBudgetInfoByProjectID(projectID),
	).Pagination(pageOptions)
}

func (s *pmoBudgetInfoService) Delete(id string) core.IError {
	// Verify record exists
	_, err := s.Find(id)
	if err != nil {
		return err
	}

	// Soft delete
	return repo.PMOBudgetInfo(s.ctx).Where("id = ?", id).Updates(map[string]interface{}{
		"deleted_by_id": s.ctx.GetUser().ID,
		"deleted_at":    utils.GetCurrentDateTime(),
	})
}

func (s *pmoBudgetInfoService) GetVersionHistory(budgetInfoID string) ([]models.PMOBudgetInfoVersion, core.IError) {
	return repo.PMOBudgetInfoVersion(s.ctx,
		repo.PMOBudgetInfoVersionByBudgetInfoID(budgetInfoID),
		repo.PMOBudgetInfoVersionOrderBy(&core.PageOptions{}),
	).Find()
}

func (s *pmoBudgetInfoService) GetVersionHistoryPagination(pageOptions *core.PageOptions, budgetInfoID string) (*repository.Pagination[models.PMOBudgetInfoVersion], core.IError) {
	return repo.PMOBudgetInfoVersion(s.ctx,
		repo.PMOBudgetInfoVersionOrderBy(pageOptions),
		repo.PMOBudgetInfoVersionByBudgetInfoID(budgetInfoID),
	).Pagination(pageOptions)
}

func (s *pmoBudgetInfoService) RestoreFromVersion(versionID string) (*models.PMOBudgetInfo, core.IError) {
	// Find the version record
	version, err := repo.PMOBudgetInfoVersion(s.ctx).FindOne("id = ?", versionID)
	if err != nil {
		return nil, s.ctx.NewError(err, "Version not found")
	}

	// Find the original record
	original, err := s.Find(version.BudgetInfoID)
	if err != nil {
		return nil, err
	}

	// Create updated data from version
	updatedData := &models.PMOBudgetInfo{
		BaseModelWithAudit: original.BaseModelWithAudit,
		ProjectID:          version.ProjectID,
		FundType:           version.FundType,
		ProjectValue:       version.ProjectValue,
		BidbondValue:       version.BidbondValue,
		Partner:            version.Partner,
	}

	// Update with version (this will create another version record)
	return s.UpdateWithVersion(original.ID, updatedData)
}

// Example usage functions

// CreateBudgetInfoWithAutoVersioning demonstrates creating budget info with automatic versioning on updates
func (s *pmoBudgetInfoService) CreateBudgetInfoWithAutoVersioning(projectID string, fundType string, projectValue, bidbondValue float64, partner string) (*models.PMOBudgetInfo, core.IError) {
	budgetInfo := &models.PMOBudgetInfo{
		ProjectID:    projectID,
		FundType:     fundType,
		ProjectValue: projectValue,
		BidbondValue: bidbondValue,
		Partner:      partner,
	}

	return s.Create(budgetInfo)
}

// UpdateBudgetInfoWithVersioning demonstrates updating with automatic versioning
func (s *pmoBudgetInfoService) UpdateBudgetInfoWithVersioning(id string, fundType string, projectValue, bidbondValue float64, partner string) (*models.PMOBudgetInfo, core.IError) {
	// Get existing record to preserve project ID
	existing, err := s.Find(id)
	if err != nil {
		return nil, err
	}

	updatedData := &models.PMOBudgetInfo{
		ProjectID:    existing.ProjectID,
		FundType:     fundType,
		ProjectValue: projectValue,
		BidbondValue: bidbondValue,
		Partner:      partner,
	}

	return s.UpdateWithVersion(id, updatedData)
}

// GetBudgetInfoWithHistory demonstrates getting budget info with its version history
func (s *pmoBudgetInfoService) GetBudgetInfoWithHistory(id string) (*models.PMOBudgetInfo, []models.PMOBudgetInfoVersion, core.IError) {
	// Get current record
	current, err := s.Find(id)
	if err != nil {
		return nil, nil, err
	}

	// Get version history
	versions, err := s.GetVersionHistory(id)
	if err != nil {
		return current, nil, err
	}

	return current, versions, nil
}
