package models

import (
	"time"

	"gitlab.finema.co/finema/idin-core/utils"
	"gorm.io/gorm"
)

type BaseModel struct {
	ID        string         `json:"id" gorm:"column:id;primary_key"`
	CreatedAt *time.Time     `json:"created_at" gorm:"column:created_at"`
	UpdatedAt *time.Time     `json:"updated_at" gorm:"column:updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"column:deleted_at"`
}

func NewBaseModel() BaseModel {
	return BaseModel{
		ID:        utils.GetUUID(),
		CreatedAt: utils.GetCurrentDateTime(),
		UpdatedAt: utils.GetCurrentDateTime(),
	}
}

type BaseModelHardDelete struct {
	ID        string     `json:"id" gorm:"column:id;primary_key"`
	CreatedAt *time.Time `json:"created_at" gorm:"column:created_at"`
	UpdatedAt *time.Time `json:"updated_at" gorm:"column:updated_at"`
}

func NewBaseModelHardDelete() BaseModelHardDelete {
	return BaseModelHardDelete{
		ID:        utils.GetUUID(),
		CreatedAt: utils.GetCurrentDateTime(),
		UpdatedAt: utils.GetCurrentDateTime(),
	}
}

// BaseModelWithAudit provides audit fields for tracking who created/updated/deleted records
type BaseModelWithAudit struct {
	BaseModel
	CreatedByID *string `json:"created_by_id" gorm:"column:created_by_id;type:uuid"`
	UpdatedByID *string `json:"updated_by_id" gorm:"column:updated_by_id;type:uuid"`
	DeletedByID *string `json:"deleted_by_id" gorm:"column:deleted_by_id;type:uuid"`
}

func NewBaseModelWithAudit() BaseModelWithAudit {
	return BaseModelWithAudit{
		BaseModel: NewBaseModel(),
	}
}

// BaseVersionModel provides a reusable structure for version models
type BaseVersionModel struct {
	BaseModelWithAudit
	OriginalID string `json:"original_id" gorm:"column:original_id;type:uuid;index"`
	ProjectID  string `json:"project_id" gorm:"column:project_id;type:uuid;index"`
}

func NewBaseVersionModel(originalID, projectID string) BaseVersionModel {
	return BaseVersionModel{
		BaseModelWithAudit: NewBaseModelWithAudit(),
		OriginalID:         originalID,
		ProjectID:          projectID,
	}
}
